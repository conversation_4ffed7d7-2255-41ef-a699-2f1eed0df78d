from Classifier import Classifier
import requests
import os
import gc
import torch
import psutil
import time

def get_memory_usage():
    """
    Vrátí aktuální využití paměti v MB.

    Returns:
        float: Využití paměti v MB
    """
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def aggressive_memory_cleanup():
    """
    Provede agresivní vyčištění paměti po dokončení tréninku modelu.
    Tato funkce by měla být volána po každém dokončeném fine-tuningu.
    """
    print("  🧹 Spouštím agresivní vyčištění paměti...")

    # Zaznamenáme využití paměti před čištěním
    memory_before = get_memory_usage()
    print(f"    Paměť před čištěním: {memory_before:.1f} MB")

    # 1. Vynucení Python garbage collection
    collected = gc.collect()
    print(f"    Garbage collection uvolnil {collected} objektů")

    # 2. Vyčištění PyTorch cache pro všechny dostupné backendy
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        print("    CUDA cache vyčištěn")

    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        if hasattr(torch, 'mps') and hasattr(torch.mps, 'empty_cache'):
            torch.mps.empty_cache()
            print("    MPS cache vyčištěn")

    # 3. Další kolo garbage collection po vyčištění cache
    gc.collect()

    # 4. Krátká pauza pro dokončení uvolňování paměti
    time.sleep(1)

    # Zaznamenáme využití paměti po čištění
    memory_after = get_memory_usage()
    memory_freed = memory_before - memory_after

    print(f"    Paměť po čištění: {memory_after:.1f} MB")
    if memory_freed > 0:
        print(f"    ✅ Uvolněno: {memory_freed:.1f} MB paměti")
    else:
        print(f"    ⚠️  Paměť se nezvýšila (možná už byla optimální)")

    return memory_freed

def monitor_memory_usage(model_name, stage=""):
    """
    Monitoruje a loguje využití paměti.

    Args:
        model_name (str): Název modelu
        stage (str): Fáze procesu (např. "před tréninkem", "po tréninku")
    """
    memory_mb = get_memory_usage()
    print(f"    📊 Paměť {stage} pro '{model_name}': {memory_mb:.1f} MB")
    return memory_mb

def check_memory_threshold(threshold_mb=8000):
    """
    Zkontroluje, zda využití paměti nepřekročilo práh.

    Args:
        threshold_mb (int): Práh v MB (výchozí 8GB)

    Returns:
        bool: True pokud je paměť pod prahem, False pokud je nad prahem
    """
    current_memory = get_memory_usage()
    if current_memory > threshold_mb:
        print(f"    ⚠️  VAROVÁNÍ: Vysoké využití paměti: {current_memory:.1f} MB (práh: {threshold_mb} MB)")
        return False
    return True

def check_model_availability(model_name):
    """
    Zkontroluje, zda je model dostupný pro stažení z Hugging Face Hub.

    Args:
        model_name (str): Název modelu

    Returns:
        bool: True pokud je model dostupný, False jinak
    """
    # Speciální případy pro modely, které nejsou standardní sentence transformers
    special_models = ['phi-2', 'TF-IDF', 'polyglot-xxl']
    if model_name in special_models:
        print(f"    Upozornění: Model '{model_name}' je speciální model, přeskakuji kontrolu dostupnosti.")
        return False

    try:
        # Zkontrolujeme, zda model existuje na Hugging Face Hub
        url = f"https://huggingface.co/api/models/{model_name}"
        response = requests.get(url, timeout=25)
        if response.status_code == 200:
            return True
        else:
            print(f"    Model '{model_name}' nebyl nalezen na Hugging Face Hub (status: {response.status_code})")
            return True
    except Exception as e:
        print(f"    Chyba při kontrole dostupnosti modelu '{model_name}': {e}")
        return False

# Statistiky pro shrnutí
successful_models = []
failed_models = []
total_models = 0

print("=== Spouštím fine-tuning pro všechny modely ===\n")

# Počáteční stav paměti
initial_memory = get_memory_usage()
print(f"Počáteční využití paměti: {initial_memory:.1f} MB\n")

for model_name in open('models.txt'):
    model_name = model_name.strip()
    if not model_name:  # Přeskočíme prázdné řádky
        continue

    total_models += 1
    print(f"\n--- Fine-tuning modelu {total_models}: {model_name} ---")

    # Monitorování paměti před začátkem
    monitor_memory_usage(model_name, "před začátkem")

    # Kontrola dostupnosti modelu
    print(f"  Kontroluji dostupnost modelu '{model_name}'...")
    if not check_model_availability(model_name):
        print(f"  ❌ Model '{model_name}' není dostupný, přeskakuji.")
        failed_models.append((model_name, "Model není dostupný"))
        continue

    print(f"  ✅ Model '{model_name}' je dostupný.")

    try:
        # Pokus o inicializaci klasifikátoru
        print(f"  Inicializuji klasifikátor pro model '{model_name}'...")
        new_classifier = Classifier(model_name=model_name)

        # Monitorování paměti po inicializaci
        monitor_memory_usage(model_name, "po inicializaci")

        # Pokus o fine-tuning
        print(f"  Spouštím fine-tuning pro model '{model_name}'...")
        new_classifier.fine_tune(
            data_dir='training_data',
            model_dir=None,  # Necháme automatické generování názvu složky podle názvu modelu
            num_epochs=1,
            train_batch_size=16,
            learning_rate=2e-5,
            validation_split=0.2
        )

        # Monitorování paměti po tréninku
        monitor_memory_usage(model_name, "po tréninku")

        print(f"  ✅ Fine-tuning modelu '{model_name}' byl úspěšně dokončen!")
        successful_models.append(model_name)

        # Explicitní uvolnění reference na klasifikátor
        del new_classifier

        # Agresivní vyčištění paměti po úspěšném tréninku
        memory_freed = aggressive_memory_cleanup()

        # Kontrola, zda paměť nepřekročila bezpečný práh
        if not check_memory_threshold():
            print(f"    ⚠️  Vysoké využití paměti po modelu '{model_name}'. Doporučuji restartovat script.")

    except Exception as e:
        print(f"  ❌ Chyba při fine-tuningu modelu '{model_name}': {e}")
        failed_models.append((model_name, str(e)))

        # I při chybě se pokusíme vyčistit paměť
        try:
            if 'new_classifier' in locals():
                del new_classifier
            aggressive_memory_cleanup()
        except:
            pass  # Ignorujeme chyby při čištění paměti

        continue

# Shrnutí výsledků
print(f"\n{'='*60}")
print("=== SHRNUTÍ FINE-TUNINGU ===")
print(f"{'='*60}")
print(f"Celkem zpracováno modelů: {total_models}")
print(f"Úspěšně dokončeno: {len(successful_models)}")
print(f"Neúspěšné: {len(failed_models)}")

if successful_models:
    print(f"\n✅ Úspěšné modely ({len(successful_models)}):")
    for model in successful_models:
        print(f"  - {model}")

if failed_models:
    print(f"\n❌ Neúspěšné modely ({len(failed_models)}):")
    for model, error in failed_models:
        print(f"  - {model}: {error}")

print(f"\n{'='*60}")

# Finální shrnutí paměti
final_memory = get_memory_usage()
total_memory_change = final_memory - initial_memory

print("=== SHRNUTÍ VYUŽITÍ PAMĚTI ===")
print(f"Počáteční paměť: {initial_memory:.1f} MB")
print(f"Finální paměť: {final_memory:.1f} MB")
if total_memory_change > 0:
    print(f"Celkový nárůst paměti: +{total_memory_change:.1f} MB")
else:
    print(f"Celková změna paměti: {total_memory_change:.1f} MB")

print(f"\n{'='*60}")
print("Fine-tuning všech modelů dokončen!")