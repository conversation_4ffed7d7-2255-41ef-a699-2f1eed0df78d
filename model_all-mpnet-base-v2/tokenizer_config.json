{"added_tokens_decoder": {"0": {"content": "<s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<pad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "</s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "<unk>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "104": {"content": "[UNK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "30526": {"content": "<mask>", "lstrip": true, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<s>", "clean_up_tokenization_spaces": false, "cls_token": "<s>", "do_lower_case": true, "eos_token": "</s>", "extra_special_tokens": {}, "mask_token": "<mask>", "max_length": 128, "model_max_length": 384, "pad_to_multiple_of": null, "pad_token": "<pad>", "pad_token_type_id": 0, "padding_side": "right", "sep_token": "</s>", "stride": 0, "strip_accents": null, "tokenize_chinese_chars": true, "tokenizer_class": "MPNetTokenizer", "truncation_side": "right", "truncation_strategy": "longest_first", "unk_token": "[UNK]"}