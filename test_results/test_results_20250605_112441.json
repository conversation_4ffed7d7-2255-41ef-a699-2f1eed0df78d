{
  "test_session": {
    "timestamp": "2025-06-05T11:24:32.019992",
    "threshold": 0.7,
    "total_phrases": 32,
    "test_phrases": [
      "Zálohová faktura",
      "Faktura - daňový doklad:",
      "suma k <PERSON>hradě",
      "Objednávka",
      "splatno dne",
      "Daňový doklad",
      "dat vyst",
      "dat. usk. zdaň. plň.",
      "date of issue",
      "IČO",
      "DIČ",
      "dodací list",
      "datum splatnosti faktury",
      "Daňové datum",
      "Bankovní účet",
      "variabilní symbol",
      "Dodavatel",
      "Odběratel",
      "Fakturujeme vám",
      "Vystaveno dne",
      "doklad ze dne",
      "plátce",
      "vat date",
      "sazba daně",
      "základ daně",
      "i<PERSON>",
      "IBAN",
      "zaplaťte nejpozději do",
      "základn<PERSON> sazba",
      "př<PERSON>jemce",
      "faktura ze dne",
      "proforma faktura"
    ]
  },
  "models": {
    "all-MiniLM-L6-v2": {
      "model_name": "all-MiniLM-L6-v2",
      "model_path": "model_all-MiniLM-L6-v2",
      "threshold": 0.7,
      "test_timestamp": "2025-06-05T11:24:37.718408",
      "categories": [
        "Faktura",
        "Číslo faktury",
        "Variabilní symbol",
        "Objednávka",
        "Číslo objednávky",
        "Dodavatel",
        "Odběratel",
        "Číslo účtu",
        "Datum vystavení",
        "Datum splatnosti",
        "DUZP",
        "Celkem k úhradě",
        "Základ DPH",
        "Sazba DPH",
        "IČO",
        "Číslo dodacího listu",
        "DIČ",
        "IBAN",
        "DPH"
      ],
      "total_phrases": 32,
      "classifications": [
        {
          "phrase": "Zálohová faktura",
          "predicted_category": "Sazba DPH",
          "similarity": 